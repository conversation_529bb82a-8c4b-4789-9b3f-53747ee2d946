# Debezium 核心组件详细分析

## 核心接口层次结构

### 1. ChangeEventSource 接口体系

<augment_code_snippet path="debezium-core/src/main/java/io/debezium/pipeline/source/spi/ChangeEventSource.java" mode="EXCERPT">
````java
public interface ChangeEventSource {
    interface ChangeEventSourceContext {
        boolean isPaused();
        boolean isRunning();
        void resumeStreaming() throws InterruptedException;
        void waitSnapshotCompletion() throws InterruptedException;
        void streamingPaused();
        void waitStreamingPaused() throws InterruptedException;
    }
}
````
</augment_code_snippet>

<augment_code_snippet path="debezium-core/src/main/java/io/debezium/pipeline/source/spi/StreamingChangeEventSource.java" mode="EXCERPT">
````java
public interface StreamingChangeEventSource<P extends Partition, O extends OffsetContext> extends ChangeEventSource {
    default void init(O offsetContext) throws InterruptedException {}
    void execute(ChangeEventSourceContext context, P partition, O offsetContext) throws InterruptedException;
    default boolean executeIteration(ChangeEventSourceContext context, P partition, O offsetContext) throws InterruptedException {
        throw new UnsupportedOperationException("Currently unsupported by the connector");
    }
    default void commitOffset(Map<String, ?> partition, Map<String, ?> offset) {}
    default O getOffsetContext() { return null; }
}
````
</augment_code_snippet>

### 2. DebeziumEngine 接口体系

<augment_code_snippet path="debezium-api/src/main/java/io/debezium/engine/DebeziumEngine.java" mode="EXCERPT">
````java
public interface DebeziumEngine<R> extends Runnable, Closeable {
    interface RecordCommitter<R> {
        void markProcessed(R record) throws InterruptedException;
        void markBatchFinished() throws InterruptedException;
    }
    
    interface ChangeConsumer<R> {
        void handleBatch(List<R> records, RecordCommitter<R> committer) throws InterruptedException;
    }
    
    interface CompletionCallback {
        void handle(boolean success, String message, Throwable error);
    }
    
    interface ConnectorCallback {
        default void connectorStarted() {}
        default void connectorStopped() {}
        default void taskStarted() {}
        default void taskStopped() {}
    }
}
````
</augment_code_snippet>

### 3. 连接器基类层次结构

<augment_code_snippet path="debezium-core/src/main/java/io/debezium/connector/common/BaseSourceConnector.java" mode="EXCERPT">
````java
public abstract class BaseSourceConnector extends SourceConnector {
    protected abstract Map<String, ConfigValue> validateAllFields(Configuration config);
    public abstract <T extends DataCollectionId> List<T> getMatchingCollections(Configuration config);
}
````
</augment_code_snippet>

## 连接器实现分析

### 1. MySQL 连接器实现

<augment_code_snippet path="debezium-connector-mysql/src/main/java/io/debezium/connector/mysql/MySqlConnector.java" mode="EXCERPT">
````java
public class MySqlConnector extends BinlogConnector<MySqlConnectorConfig> {
    @Override
    public String version() { return Module.version(); }
    
    @Override
    public Class<? extends Task> taskClass() { return MySqlConnectorTask.class; }
    
    @Override
    public ConfigDef config() { return MySqlConnectorConfig.configDef(); }
}
````
</augment_code_snippet>

<augment_code_snippet path="debezium-connector-mysql/src/main/java/io/debezium/connector/mysql/MySqlStreamingChangeEventSource.java" mode="EXCERPT">
````java
public class MySqlStreamingChangeEventSource extends BinlogStreamingChangeEventSource<MySqlPartition, MySqlOffsetContext> {
    @Override
    protected void handleGtidEvent(MySqlOffsetContext offsetContext, Event event) {
        final EventData eventData = unwrapData(event);
        if (eventData instanceof GtidEventData) {
            String gtid = ((GtidEventData) eventData).getGtid();
            String uuid = gtid.trim().substring(0, gtid.indexOf(":"));
            if (!gtidSourceFilter.test(uuid)) {
                setIgnoreDmlEventByGtidSource(true);
            }
        }
        setGtidChanged(gtid);
    }
}
````
</augment_code_snippet>

### 2. PostgreSQL 连接器实现

<augment_code_snippet path="debezium-connector-postgres/src/main/java/io/debezium/connector/postgresql/PostgresConnector.java" mode="EXCERPT">
````java
public class PostgresConnector extends RelationalBaseSourceConnector {
    @Override
    public String version() { return Module.version(); }
    
    @Override
    public Class<? extends Task> taskClass() { return PostgresConnectorTask.class; }
    
    @Override
    public ConfigDef config() { return PostgresConnectorConfig.configDef(); }
}
````
</augment_code_snippet>

### 3. MongoDB 连接器实现

<augment_code_snippet path="debezium-connector-mongodb/src/main/java/io/debezium/connector/mongodb/MongoDbConnector.java" mode="EXCERPT">
````java
public class MongoDbConnector extends BaseSourceConnector {
    @Override
    public void start(Map<String, String> props) {
        final Configuration config = Configuration.from(props);
        if (!config.validateAndRecord(MongoDbConnectorConfig.ALL_FIELDS, LOGGER::error)) {
            throw new DebeziumException("Error configuring an instance of " + getClass().getSimpleName());
        }
        this.config = config;
    }
    
    @Override
    public List<Map<String, String>> taskConfigs(int maxTasks) {
        if (config == null) {
            return Collections.emptyList();
        }
        return List.of(config.asMap());
    }
}
````
</augment_code_snippet>

## 存储组件实现分析

### 1. Redis 偏移量存储

<augment_code_snippet path="debezium-storage/debezium-storage-redis/src/main/java/io/debezium/storage/redis/offset/RedisOffsetBackingStore.java" mode="EXCERPT">
````java
public class RedisOffsetBackingStore extends MemoryOffsetBackingStore {
    void connect() {
        closeClient();
        RedisConnection redisConnection = new RedisConnection(config.getAddress(), config.getDbIndex(), 
            config.getUser(), config.getPassword(), config.getConnectionTimeout(), config.getSocketTimeout(), 
            config.isSslEnabled());
        client = redisConnection.getRedisClient(RedisConnection.DEBEZIUM_OFFSETS_CLIENT_NAME, 
            config.isWaitEnabled(), config.getWaitTimeout(), config.isWaitRetryEnabled(), config.getWaitRetryDelay());
    }
    
    @Override
    protected void save() {
        for (Map.Entry<ByteBuffer, ByteBuffer> mapEntry : data.entrySet()) {
            byte[] key = (mapEntry.getKey() != null) ? mapEntry.getKey().array() : null;
            byte[] value = (mapEntry.getValue() != null) ? mapEntry.getValue().array() : null;
            // 使用 Redis HSET 命令存储偏移量
            client.hset(config.getRedisKeyName().getBytes(), key, value);
        }
    }
}
````
</augment_code_snippet>

### 2. JDBC 偏移量存储

<augment_code_snippet path="debezium-storage/debezium-storage-jdbc/src/main/java/io/debezium/storage/jdbc/offset/JdbcOffsetBackingStore.java" mode="EXCERPT">
````java
public class JdbcOffsetBackingStore implements OffsetBackingStore {
    protected void save() {
        conn.executeWithRetry((conn) -> {
            try (PreparedStatement sqlDelete = conn.prepareStatement(config.getTableDelete())) {
                sqlDelete.executeUpdate();
                for (Map.Entry<String, String> mapEntry : data.entrySet()) {
                    Timestamp currentTs = new Timestamp(System.currentTimeMillis());
                    String key = mapEntry.getKey();
                    String value = mapEntry.getValue();
                    try (PreparedStatement sql = conn.prepareStatement(config.getTableInsert())) {
                        sql.setString(1, UUID.randomUUID().toString());
                        sql.setString(2, key);
                        sql.setString(3, value);
                        sql.setTimestamp(4, currentTs);
                        sql.executeUpdate();
                    }
                }
            }
            return null;
        });
    }
}
````
</augment_code_snippet>

### 3. Redis Schema 历史存储

<augment_code_snippet path="debezium-storage/debezium-storage-redis/src/main/java/io/debezium/storage/redis/history/RedisSchemaHistory.java" mode="EXCERPT">
````java
public class RedisSchemaHistory extends AbstractSchemaHistory {
    @Override
    protected void storeRecord(HistoryRecord record) throws SchemaHistoryException {
        if (record == null) return;
        
        String line = writer.write(record.document());
        doWithRetry(() -> {
            // 使用 Redis Streams 存储 Schema 历史
            client.xadd(config.getRedisKeyName(), Collections.singletonMap("schema", line));
            return true;
        }, "Writing to database schema history stream");
    }
    
    @Override
    protected synchronized void recoverRecords(Consumer<HistoryRecord> records) {
        final List<Map<String, String>> entries = doWithRetry(() -> 
            client.xrange(config.getRedisKeyName()), "Reading from database schema history stream");
        
        for (Map<String, String> item : entries) {
            records.accept(new HistoryRecord(reader.read(item.get("schema"))));
        }
    }
}
````
</augment_code_snippet>

## 引擎实现分析

### 1. EmbeddedEngine (已废弃)

<augment_code_snippet path="debezium-embedded/src/main/java/io/debezium/embedded/EmbeddedEngine.java" mode="EXCERPT">
````java
@Deprecated
public final class EmbeddedEngine implements DebeziumEngine<SourceRecord>, EmbeddedEngineConfig {
    @Override
    public void run() {
        // 单线程执行模式
        final SourceConnector connector = instantiateConnector(connectorClassName);
        final OffsetBackingStore offsetStore = initializeOffsetStore(connectorConfig);
        
        // 轮询记录并处理
        pollRecords(taskConfigs, committer, errors);
    }
}
````
</augment_code_snippet>

### 2. AsyncEmbeddedEngine

<augment_code_snippet path="debezium-embedded/src/main/java/io/debezium/embedded/async/AsyncEmbeddedEngine.java" mode="EXCERPT">
````java
public final class AsyncEmbeddedEngine<R> implements DebeziumEngine<R>, AsyncEngineConfig {
    @Override
    public void run() {
        try {
            setEngineState(State.CREATING, State.INITIALIZING);
            connector.connectConnector().start(initializeConnector());
            
            setEngineState(State.INITIALIZING, State.CREATING_TASKS);
            createSourceTasks(connector, tasks);
            
            setEngineState(State.CREATING_TASKS, State.STARTING_TASKS);
            startSourceTasks(tasks);
            
            setEngineState(State.STARTING_TASKS, State.POLLING_TASKS);
            runTasksPolling(tasks); // 支持多任务并行处理
        } catch (Throwable t) {
            closeEngineWithException(t);
        }
    }
}
````
</augment_code_snippet>

## REST API 扩展分析

### 1. 主要 REST 扩展

<augment_code_snippet path="debezium-connect-rest-extension/src/main/java/io/debezium/kcrestextension/DebeziumResource.java" mode="EXCERPT">
````java
@Path(DebeziumResource.BASE_PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class DebeziumResource {
    public static final String BASE_PATH = "/debezium";
    public static final String CONNECTOR_PLUGINS_ENDPOINT = "/connector-plugins";
    public static final String TRANSFORMS_ENDPOINT = "/transforms";
    public static final String PREDICATES_ENDPOINT = "/predicates";
    
    @GET
    @Path(CONNECTOR_PLUGINS_ENDPOINT)
    public Set<ConnectorDescriptor> availableDebeziumConnectors() {
        initConnectorPlugins();
        return this.availableConnectorPlugins;
    }
    
    @GET
    @Path("/version")
    public String getDebeziumVersion() {
        return Module.version();
    }
}
````
</augment_code_snippet>

### 2. 连接器特定的 REST 资源

<augment_code_snippet path="debezium-connector-mysql/src/main/java/io/debezium/connector/mysql/rest/DebeziumMySqlConnectorResource.java" mode="EXCERPT">
````java
@Path(DebeziumMySqlConnectorResource.BASE_PATH)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class DebeziumMySqlConnectorResource 
    implements SchemaResource, ConnectionValidationResource, FilterValidationResource, MetricsResource {
    
    public static final String BASE_PATH = "/debezium/mysql";
    public static final String VERSION_ENDPOINT = "/version";
    
    @Override
    public String getSchemaFilePath() {
        return "/META-INF/resources/mysql.json";
    }
}
````
</augment_code_snippet>

## 关键设计模式

### 1. 策略模式
- **OffsetBackingStore**: 不同的偏移量存储策略
- **SchemaHistory**: 不同的Schema历史存储策略
- **ChangeEventSource**: 不同的变更事件源策略

### 2. 工厂模式
- **ChangeEventSourceFactory**: 创建变更事件源
- **ConnectorFactory**: 创建连接器实例

### 3. 观察者模式
- **EventDispatcher**: 事件分发机制
- **ChangeConsumer**: 变更事件消费者

### 4. 模板方法模式
- **AbstractSchemaHistory**: Schema历史管理模板
- **BaseSourceConnector**: 连接器基类模板

## 配置管理体系

### 1. 配置验证机制
- 每个连接器都有对应的 `*ConnectorConfig` 类
- 使用 Kafka Connect 的 `ConfigDef` 进行配置定义和验证
- 支持配置字段的依赖关系和条件验证

### 2. 配置继承层次
```
CommonConnectorConfig
├── RelationalDatabaseConnectorConfig
│   ├── MySqlConnectorConfig
│   ├── PostgresConnectorConfig
│   └── SqlServerConnectorConfig
└── MongoDbConnectorConfig
```

这个详细的组件分析展示了 Debezium 的核心架构实现，包括接口设计、具体实现和关键设计模式的应用。
