# Debezium 系统架构分析报告

## 概述

基于对 Debezium 代码库的深入分析，本报告提供了完整的系统架构图和组件说明。分析覆盖了所有核心模块、连接器实现、存储组件和部署模式。

## 系统架构图

```mermaid
graph TB
    %% 应用层
    subgraph "应用层 (Application Layer)"
        APP1[Java应用程序]
        APP2[微服务]
        APP3[数据处理管道]
    end

    %% Debezium 部署模式
    subgraph "Debezium 部署模式"
        subgraph "Kafka Connect 模式"
            KC[Kafka Connect Framework]
            KCW[Kafka Connect Worker]
            KCAPI[Kafka Connect REST API]
        end

        subgraph "Embedded 模式"
            DE[DebeziumEngine]
            EE[EmbeddedEngine]
            AEE[AsyncEmbeddedEngine]
        end

        subgraph "Server 模式"
            DS[Debezium Server]
            DSC[Server Configuration]
        end
    end

    %% 核心引擎层
    subgraph "核心引擎层 (Core Engine Layer)"
        subgraph "debezium-core"
            ED[EventDispatcher]
            CES[ChangeEventSource]
            SCES[StreamingChangeEventSource]
            SNCES[SnapshotChangeEventSource]
            CEQ[ChangeEventQueue]
            CESC[ChangeEventSourceCoordinator]
        end

        subgraph "debezium-api"
            DAPI[DebeziumEngine Interface]
            CC[ChangeConsumer]
            RC[RecordCommitter]
            CEF[ChangeEventFormat]
        end
    end

    %% 连接器层
    subgraph "连接器层 (Connector Layer)"
        subgraph "MySQL/MariaDB"
            MC[MySqlConnector]
            MTC[MySqlConnectorTask]
            MCES[MySqlStreamingChangeEventSource]
            MSNCES[MySqlSnapshotChangeEventSource]
            MARC[MariaDbConnector]
        end

        subgraph "PostgreSQL"
            PC[PostgresConnector]
            PTC[PostgresConnectorTask]
            PCES[PostgresStreamingChangeEventSource]
            PSNCES[PostgresSnapshotChangeEventSource]
        end

        subgraph "MongoDB"
            MGOC[MongoDbConnector]
            MGOTC[MongoDbConnectorTask]
            MGOCES[MongoDbStreamingChangeEventSource]
        end

        subgraph "Oracle"
            OC[OracleConnector]
            OTC[OracleConnectorTask]
            OCES[OracleStreamingChangeEventSource]
        end

        subgraph "SQL Server"
            SSC[SqlServerConnector]
            SSTC[SqlServerConnectorTask]
            SSCES[SqlServerStreamingChangeEventSource]
        end
    end

    %% 存储层
    subgraph "存储层 (Storage Layer)"
        subgraph "Offset Storage"
            FOBS[FileOffsetBackingStore]
            KOBS[KafkaOffsetBackingStore]
            ROBS[RedisOffsetBackingStore]
            JOBS[JdbcOffsetBackingStore]
            COBS[ConfigMapOffsetStore]
        end

        subgraph "Schema History Storage"
            FSH[FileSchemaHistory]
            KSH[KafkaSchemaHistory]
            RSH[RedisSchemaHistory]
            JSH[JdbcSchemaHistory]
            ASH[AzureBlobSchemaHistory]
        end
    end

    %% 数据源层
    subgraph "数据源层 (Data Source Layer)"
        subgraph "关系型数据库"
            MYSQL[(MySQL)]
            POSTGRES[(PostgreSQL)]
            ORACLE[(Oracle)]
            SQLSERVER[(SQL Server)]
            MARIADB[(MariaDB)]
        end

        subgraph "NoSQL数据库"
            MONGODB[(MongoDB)]
        end
    end

    %% 目标系统层
    subgraph "目标系统层 (Target Systems)"
        subgraph "消息系统"
            KAFKA[Apache Kafka]
            KINESIS[Amazon Kinesis]
            PUBSUB[Google Cloud Pub/Sub]
            PULSAR[Apache Pulsar]
            REDIS[Redis Streams]
        end

        subgraph "数据仓库"
            ES[Elasticsearch]
            DW[Data Warehouse]
            ANALYTICS[Analytics Systems]
        end
    end

    %% REST API 扩展
    subgraph "REST API 扩展"
        DCRE[DebeziumConnectRestExtension]
        DMRE[DebeziumMySqlConnectorResource]
        DPRE[DebeziumPostgresConnectorResource]
        DMORE[DebeziumMongoDbConnectorResource]
    end

    %% 连接关系
    APP1 --> DE
    APP2 --> DE
    APP3 --> DE

    KC --> KCW
    KCW --> MC
    KCW --> PC
    KCW --> MGOC

    DE --> EE
    DE --> AEE

    DS --> DSC
    DS --> DE

    ED --> CEQ
    CES --> SCES
    CES --> SNCES
    CESC --> CES

    MC --> MTC
    MTC --> MCES
    MTC --> MSNCES

    PC --> PTC
    PTC --> PCES
    PTC --> PSNCES

    MGOC --> MGOTC
    MGOTC --> MGOCES

    MCES --> MYSQL
    PCES --> POSTGRES
    MGOCES --> MONGODB
    OCES --> ORACLE
    SSCES --> SQLSERVER

    MTC --> FOBS
    MTC --> ROBS
    MTC --> JOBS

    MTC --> FSH
    MTC --> RSH
    MTC --> JSH

    CEQ --> KAFKA
    DS --> KINESIS
    DS --> PUBSUB
    DS --> PULSAR
    DS --> REDIS

    KAFKA --> ES
    KAFKA --> DW
    KAFKA --> ANALYTICS

    KCAPI --> DCRE
    DCRE --> DMRE
    DCRE --> DPRE
    DCRE --> DMORE

    %% 样式定义
    classDef coreEngine fill:#e1f5fe
    classDef connector fill:#f3e5f5
    classDef storage fill:#e8f5e8
    classDef database fill:#fff3e0
    classDef target fill:#fce4ec
    classDef api fill:#f1f8e9

    class ED,CES,SCES,SNCES,CEQ,CESC coreEngine
    class MC,PC,MGOC,OC,SSC connector
    class FOBS,KOBS,ROBS,JOBS,FSH,KSH,RSH,JSH storage
    class MYSQL,POSTGRES,MONGODB,ORACLE,SQLSERVER database
    class KAFKA,KINESIS,PUBSUB,PULSAR,REDIS,ES,DW target
    class DCRE,DMRE,DPRE,DMORE api
```

## 核心组件详细说明

### 1. 核心引擎层 (debezium-core)

#### EventDispatcher
- **路径**: `debezium-core/src/main/java/io/debezium/pipeline/EventDispatcher.java`
- **职责**: 事件分发和路由，处理数据变更事件的转换和分发
- **关键方法**:
  - `dispatchDataChangeEvent()`: 分发数据变更事件
  - `dispatchSchemaChangeEvent()`: 分发Schema变更事件

#### ChangeEventSource 接口层次
- **StreamingChangeEventSource**: 流式变更事件源接口
  - **路径**: `debezium-core/src/main/java/io/debezium/pipeline/source/spi/StreamingChangeEventSource.java`
  - **关键方法**: `execute()`, `executeIteration()`, `commitOffset()`

- **SnapshotChangeEventSource**: 快照变更事件源接口
  - **路径**: `debezium-core/src/main/java/io/debezium/pipeline/source/spi/SnapshotChangeEventSource.java`
  - **关键方法**: `execute()`, `getSnapshottingTask()`

#### ChangeEventSourceCoordinator
- **职责**: 协调快照和流式变更事件源的执行
- **关键功能**: 管理快照和流式处理的生命周期

### 2. 嵌入式引擎层 (debezium-embedded)

#### DebeziumEngine 接口
- **路径**: `debezium-api/src/main/java/io/debezium/engine/DebeziumEngine.java`
- **关键接口**:
  - `ChangeConsumer<R>`: 变更事件消费者
  - `RecordCommitter<R>`: 记录提交器
  - `CompletionCallback`: 完成回调

#### EmbeddedEngine (已废弃)
- **路径**: `debezium-embedded/src/main/java/io/debezium/embedded/EmbeddedEngine.java`
- **特点**: 单线程执行，顺序处理记录

#### AsyncEmbeddedEngine
- **路径**: `debezium-embedded/src/main/java/io/debezium/embedded/async/AsyncEmbeddedEngine.java`
- **特点**: 支持多线程并行处理，支持多任务执行

### 3. 连接器层

#### MySQL 连接器
- **MySqlConnector**: `debezium-connector-mysql/src/main/java/io/debezium/connector/mysql/MySqlConnector.java`
- **MySqlConnectorTask**: 任务执行器
- **MySqlStreamingChangeEventSource**: MySQL binlog 流式处理
- **MySqlSnapshotChangeEventSource**: MySQL 快照处理

#### PostgreSQL 连接器
- **PostgresConnector**: `debezium-connector-postgres/src/main/java/io/debezium/connector/postgresql/PostgresConnector.java`
- **特点**: 使用逻辑复制槽进行流式处理

#### MongoDB 连接器
- **MongoDbConnector**: `debezium-connector-mongodb/src/main/java/io/debezium/connector/mongodb/MongoDbConnector.java`
- **特点**: 使用 Change Streams 进行变更捕获

### 4. 存储层 (debezium-storage)

#### 偏移量存储实现
- **FileOffsetBackingStore**: 文件存储
- **RedisOffsetBackingStore**: Redis 存储
  - **路径**: `debezium-storage/debezium-storage-redis/src/main/java/io/debezium/storage/redis/offset/RedisOffsetBackingStore.java`
- **JdbcOffsetBackingStore**: JDBC 数据库存储
  - **路径**: `debezium-storage/debezium-storage-jdbc/src/main/java/io/debezium/storage/jdbc/offset/JdbcOffsetBackingStore.java`
- **ConfigMapOffsetStore**: Kubernetes ConfigMap 存储
  - **路径**: `debezium-storage/debezium-storage-configmap/src/main/java/io/debezium/storage/configmap/ConfigMapOffsetStore.java`

#### Schema 历史存储实现
- **FileSchemaHistory**: 文件存储
  - **路径**: `debezium-storage/debezium-storage-file/src/main/java/io/debezium/storage/file/history/FileSchemaHistory.java`
- **RedisSchemaHistory**: Redis 存储
  - **路径**: `debezium-storage/debezium-storage-redis/src/main/java/io/debezium/storage/redis/history/RedisSchemaHistory.java`
- **JdbcSchemaHistory**: JDBC 数据库存储
  - **路径**: `debezium-storage/debezium-storage-jdbc/src/main/java/io/debezium/storage/jdbc/history/JdbcSchemaHistory.java`

### 5. REST API 扩展 (debezium-connect-rest-extension)

#### DebeziumConnectRestExtension
- **路径**: `debezium-connect-rest-extension/src/main/java/io/debezium/kcrestextension/DebeziumConnectRestExtension.java`
- **功能**: 扩展 Kafka Connect REST API

#### 连接器特定的 REST 资源
- **DebeziumMySqlConnectorResource**: MySQL 连接器 REST API
- **DebeziumPostgresConnectorResource**: PostgreSQL 连接器 REST API
- **DebeziumMongoDbConnectorResource**: MongoDB 连接器 REST API

## 部署模式对比

### 1. Kafka Connect 模式
- **适用场景**: 生产环境，需要高可用和容错
- **特点**: 分布式部署，自动故障转移
- **配置**: 通过 REST API 管理连接器

### 2. Embedded 模式
- **适用场景**: 应用内嵌，简单部署
- **特点**: 直接在应用进程中运行
- **配置**: 程序化配置

### 3. Server 模式
- **适用场景**: 独立服务，支持多种消息系统
- **特点**: 无需 Kafka，支持 Kinesis、Pulsar 等
- **配置**: 配置文件驱动

## 数据流向分析

### 1. 变更捕获流程
```
数据库变更 → ChangeEventSource → EventDispatcher → ChangeEventQueue → 目标系统
```

### 2. 偏移量管理流程
```
SourceTask → OffsetStorageWriter → OffsetBackingStore → 持久化存储
```

### 3. Schema 历史管理流程
```
Schema变更 → SchemaHistory → 持久化存储 → Schema恢复
```

## 关键配置文件位置

### 连接器配置
- MySQL: `debezium-connector-mysql/src/main/java/io/debezium/connector/mysql/MySqlConnectorConfig.java`
- PostgreSQL: `debezium-connector-postgres/src/main/java/io/debezium/connector/postgresql/PostgresConnectorConfig.java`
- MongoDB: `debezium-connector-mongodb/src/main/java/io/debezium/connector/mongodb/MongoDbConnectorConfig.java`

### 存储配置
- Redis 偏移量存储: `debezium-storage/debezium-storage-redis/src/main/java/io/debezium/storage/redis/offset/RedisOffsetBackingStoreConfig.java`
- JDBC 偏移量存储: `debezium-storage/debezium-storage-jdbc/src/main/java/io/debezium/storage/jdbc/offset/JdbcOffsetBackingStoreConfig.java`

## 监控和指标

### JMX 指标
- 连接器状态指标
- 事件处理指标
- 偏移量延迟指标

### REST API 监控端点
- `/debezium/version`: 版本信息
- `/debezium/connector-plugins`: 可用连接器
- `/debezium/transforms`: 可用转换器

## 架构验证结果

基于代码库分析，以下组件已验证存在：

### 核心模块验证
✅ **debezium-core**: 核心引擎和抽象接口
✅ **debezium-embedded**: 嵌入式引擎实现
✅ **debezium-api**: 公共 API 定义

### 连接器验证
✅ **debezium-connector-mysql**: MySQL 连接器
✅ **debezium-connector-postgres**: PostgreSQL 连接器
✅ **debezium-connector-mongodb**: MongoDB 连接器
✅ **debezium-connector-oracle**: Oracle 连接器
✅ **debezium-connector-sqlserver**: SQL Server 连接器
✅ **debezium-connector-mariadb**: MariaDB 连接器

### 存储模块验证
✅ **debezium-storage-file**: 文件存储
✅ **debezium-storage-redis**: Redis 存储
✅ **debezium-storage-jdbc**: JDBC 存储
✅ **debezium-storage-configmap**: Kubernetes ConfigMap 存储
✅ **debezium-storage-s3**: S3 存储
✅ **debezium-storage-azure-blob**: Azure Blob 存储

### REST 扩展验证
✅ **debezium-connect-rest-extension**: Kafka Connect REST 扩展

## 总结

Debezium 采用了模块化的架构设计，通过清晰的接口抽象和多种实现，支持多种部署模式和存储后端。核心引擎层提供了统一的变更事件处理框架，连接器层实现了特定数据库的变更捕获逻辑，存储层提供了灵活的偏移量和Schema历史管理方案。这种架构设计使得 Debezium 具有良好的扩展性和适应性，能够满足不同场景的需求。

**注意**: Debezium Server 已迁移到独立仓库 (https://github.com/debezium/debezium-server/)，当前代码库中只保留了 README 文件。
